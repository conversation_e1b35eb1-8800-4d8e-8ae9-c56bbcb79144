import os
import asyncio
import threading
import logging

from flask import Flask, request, jsonify
from pathlib import Path

from browser_runner_constants import BrowserRunnerConstants
from conf import BASE_DIR, rabbitmq_setting
from pika_client import <PERSON>ka<PERSON>lient
from race_data_runner import RaceData<PERSON>unner
from logging_config import setup_app_logging

app = Flask(__name__)

# 设置Flask的werkzeug日志级别
logging.getLogger('werkzeug').setLevel(logging.ERROR)

# 设置应用日志
logger = setup_app_logging()

loop = None

@app.route('/get_races', methods=['POST'])
def get_races():
    race_data_runner = RaceDataRunner()
    loop.run_until_complete(race_data_runner.start())
    return getResponse(True, None)

def getResponse(success, data):
    code = 200
    if not success:
        code = 500
    return jsonify({"code": code, "data": data})


def get_account_file(account):
    file_name = account + ".json"
    account_file = Path(BASE_DIR / "douyin_uploader" / file_name)
    return account_file


async def main(loop):
    BrowserRunnerConstants.PIKA_CLIENT = PikaClient(rabbitmq_setting)
    await BrowserRunnerConstants.PIKA_CLIENT.connect(loop)
    print(f'pika_client: {BrowserRunnerConstants.PIKA_CLIENT}')

if __name__ == "__main__":
    loop = asyncio.get_event_loop()
    loop.run_until_complete(main(loop))
    app.run(host='0.0.0.0', port=8989, debug=False, threaded=True)
