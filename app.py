import os
from logging.handlers import Rotating<PERSON><PERSON><PERSON>andler

from flask import Flask, request, jsonify
from pathlib import Path

from browser_runner_constants import BrowserRunnerConstants
from conf import BASE_DIR, rabbitmq_setting
import asyncio
import threading
import logging

from pika_client import PikaClient
from race_data_runner import RaceDataRunner

app = Flask(__name__)

logging.getLogger('werkzeug').setLevel(logging.ERROR)

log_dir = "logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)
# Set up logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# Log to file
log_file = os.path.join(log_dir, "info.log")
file_handler = RotatingFileHandler(log_file, maxBytes=10240, backupCount=10)
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)

# Log to console
console_handler = logging.StreamHandler()
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)

loop = None

@app.route('/get_races', methods=['POST'])
def get_races():
    race_data_runner = RaceDataRunner()
    loop.run_until_complete(race_data_runner.start())
    return getResponse(True, None)

def getResponse(success, data):
    code = 200
    if not success:
        code = 500
    return jsonify({"code": code, "data": data})


def get_account_file(account):
    file_name = account + ".json"
    account_file = Path(BASE_DIR / "douyin_uploader" / file_name)
    return account_file


async def main(loop):
    BrowserRunnerConstants.PIKA_CLIENT = PikaClient(rabbitmq_setting)
    await BrowserRunnerConstants.PIKA_CLIENT.connect(loop)
    print(f'pika_client: {BrowserRunnerConstants.PIKA_CLIENT}')

if __name__ == "__main__":
    loop = asyncio.get_event_loop()
    loop.run_until_complete(main(loop))
    app.run(host='0.0.0.0', port=8989, debug=False, threaded=True)
