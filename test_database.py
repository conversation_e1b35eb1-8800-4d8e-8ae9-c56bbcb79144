#!/usr/bin/env python3
"""
测试数据库功能的脚本
"""
import asyncio
import logging
from datetime import datetime
from mysql_client import MySQLClient
from conf import mysql_setting

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_database.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)


async def test_database_connection():
    """测试数据库连接"""
    logger.info("测试数据库连接...")
    
    mysql_client = MySQLClient(mysql_setting)
    
    try:
        await mysql_client.connect()
        logger.info("✅ 数据库连接成功")
        return mysql_client
    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        return None


async def test_insert_single_match(mysql_client):
    """测试插入单条比赛数据"""
    logger.info("测试插入单条比赛数据...")
    
    try:
        # 测试数据
        league_name = "英超"
        home_team = "曼联"
        away_team = "利物浦"
        match_time = datetime(2024, 1, 15, 20, 0, 0)
        score = "2-1"
        home_score = 2
        away_score = 1
        match_status = "已结束"
        
        success = await mysql_client.insert_match(
            league_name=league_name,
            home_team=home_team,
            away_team=away_team,
            match_time=match_time,
            score=score,
            home_score=home_score,
            away_score=away_score,
            match_status=match_status
        )
        
        if success:
            logger.info("✅ 单条数据插入成功")
        else:
            logger.info("ℹ️ 数据已存在，未插入")
            
    except Exception as e:
        logger.error(f"❌ 单条数据插入失败: {e}")


async def test_insert_batch_matches(mysql_client):
    """测试批量插入比赛数据"""
    logger.info("测试批量插入比赛数据...")
    
    try:
        # 测试数据
        matches = [
            {
                'league_name': '西甲',
                'home_team': '皇马',
                'away_team': '巴萨',
                'match_time': datetime(2024, 1, 16, 21, 0, 0),
                'score': '3-1',
                'home_score': 3,
                'away_score': 1,
                'match_status': '已结束'
            },
            {
                'league_name': '德甲',
                'home_team': '拜仁',
                'away_team': '多特',
                'match_time': datetime(2024, 1, 17, 19, 30, 0),
                'score': None,
                'home_score': None,
                'away_score': None,
                'match_status': '未开始'
            },
            {
                'league_name': '意甲',
                'home_team': '尤文',
                'away_team': 'AC米兰',
                'match_time': datetime(2024, 1, 18, 22, 0, 0),
                'score': '1-1',
                'home_score': 1,
                'away_score': 1,
                'match_status': '已结束'
            }
        ]
        
        inserted_count = await mysql_client.insert_matches_batch(matches)
        logger.info(f"✅ 批量插入成功，插入了 {inserted_count} 条数据")
        
    except Exception as e:
        logger.error(f"❌ 批量插入失败: {e}")


async def test_query_matches(mysql_client):
    """测试查询比赛数据"""
    logger.info("测试查询比赛数据...")
    
    try:
        # 查询2024-01-15的比赛
        matches = await mysql_client.get_matches_by_date('2024-01-15')
        logger.info(f"✅ 查询成功，找到 {len(matches)} 条比赛数据")
        
        for match in matches:
            logger.info(f"  - {match['league_name']}: {match['home_team']} vs {match['away_team']} "
                       f"({match['score'] or 'vs'}) - {match['match_status']}")
            
    except Exception as e:
        logger.error(f"❌ 查询失败: {e}")


async def test_score_parsing(mysql_client):
    """测试比分解析功能"""
    logger.info("测试比分解析功能...")
    
    test_scores = [
        "2-1",
        "0:3", 
        "1 - 2",
        "vs",
        "延期",
        "取消",
        "",
        "3-3"
    ]
    
    for score_str in test_scores:
        score, home_score, away_score, status = mysql_client.parse_score(score_str)
        logger.info(f"  '{score_str}' -> 比分:{score}, 主队:{home_score}, 客队:{away_score}, 状态:{status}")


async def main():
    """主测试函数"""
    logger.info("开始数据库功能测试...")
    
    # 测试数据库连接
    mysql_client = await test_database_connection()
    if not mysql_client:
        return
    
    try:
        # 测试比分解析
        await test_score_parsing(mysql_client)
        
        # 测试插入单条数据
        await test_insert_single_match(mysql_client)
        
        # 测试批量插入
        await test_insert_batch_matches(mysql_client)
        
        # 测试查询
        await test_query_matches(mysql_client)
        
        logger.info("✅ 所有测试完成")
        
    finally:
        # 关闭数据库连接
        await mysql_client.close()
        logger.info("数据库连接已关闭")


if __name__ == "__main__":
    asyncio.run(main())
