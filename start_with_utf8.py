#!/usr/bin/env python3
"""
启动脚本 - 自动修复编码后运行主程序
"""
import os
import sys
import subprocess


def setup_utf8_environment():
    """设置UTF-8环境"""
    # 设置环境变量
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
    
    # Windows特定设置
    if os.name == 'nt':
        try:
            # 设置控制台代码页
            subprocess.run(['chcp', '65001'], shell=True, capture_output=True)
        except:
            pass
        
        # 重新配置标准输出
        try:
            if hasattr(sys.stdout, 'reconfigure'):
                sys.stdout.reconfigure(encoding='utf-8')
            if hasattr(sys.stderr, 'reconfigure'):
                sys.stderr.reconfigure(encoding='utf-8')
        except:
            pass


def run_app():
    """运行主应用"""
    setup_utf8_environment()
    
    print("🚀 启动比赛数据采集器...")
    print("🔧 已设置UTF-8编码环境")
    
    # 导入并运行主应用
    try:
        from app import app
        app.run(host='0.0.0.0', port=8989, debug=False, threaded=True)
    except Exception as e:
        print(f"❌ 启动失败: {e}")


def run_test():
    """运行测试"""
    setup_utf8_environment()
    
    print("🧪 运行数据库测试...")
    
    try:
        import asyncio
        from test_database import main
        asyncio.run(main())
    except Exception as e:
        print(f"❌ 测试失败: {e}")


def run_manager():
    """运行数据库管理工具"""
    setup_utf8_environment()
    
    print("🗃️ 启动数据库管理工具...")
    
    try:
        import asyncio
        from database_manager import main
        asyncio.run(main())
    except Exception as e:
        print(f"❌ 启动失败: {e}")


if __name__ == "__main__":
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == 'app':
            run_app()
        elif command == 'test':
            run_test()
        elif command == 'manager':
            run_manager()
        else:
            print("用法:")
            print("  python start_with_utf8.py app      # 启动主应用")
            print("  python start_with_utf8.py test     # 运行测试")
            print("  python start_with_utf8.py manager  # 启动管理工具")
    else:
        # 默认启动主应用
        run_app()
