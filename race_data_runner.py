import asyncio
from pathlib import Path

from playwright.async_api import expect

from browser_runner import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from browser_runner_constants import BrowserRunnerConstants
from conf import BASE_DIR

import secrets
import string

import logging

logger = logging.getLogger()

class RaceDataRunner:
    def __init__(self):
        pid = self.generate_nano_id()
        file_name = pid + ".json"
        self.base_path = Path(BASE_DIR / "data")
        self.data_file = self.base_path / file_name

    async def start(self):
        browser_runner = BrowserRunner(self.process)
        await browser_runner.start()

    async def process(self, page):
        await page.goto("https://data.7m.com.cn/fixture_data/default_big.shtml?date=1")
        table = page.locator("#fixture_tb")
        trs = table.locator("tr")
        tr_all = await trs.all()
        for tr in tr_all:
            tds = tr.locator("td:not(:empty)")
            tr_all = await tds.all()
            row_str = ''
            for td in tr_all:
                txt = await td.inner_text()
                if txt.startswith('202'):
                    txt = txt[0:11]
                    print(f'日期是{txt}')
                elif txt.strip():
                    row_str += txt + ','
            logger.info(row_str)
            if (row_str.strip()):
                await BrowserRunnerConstants.PIKA_CLIENT.publish(BrowserRunnerConstants.PIKA_CLIENT.queue, row_str)
        # if td_dates:
        #     for td_date in td_dates:
        #         td_text = await td_date.get_attribute('colspan')
        #         # td_text = await td_date.inner_text()
        #         print(f"日期是 {td_text}")

    def generate_nano_id(self, length=10):
        # 定义可用字符集
        alphabet = string.ascii_letters + string.digits
        # 生成指定长度的随机字符串
        nano_id = ''.join(secrets.choice(alphabet) for _ in range(length))
        return nano_id
