import asyncio
from pathlib import Path
from datetime import datetime, timed<PERSON>ta
import re

from playwright.async_api import expect

from browser_runner import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from browser_runner_constants import B<PERSON>erRunnerConstants
from conf import BASE_DIR, mysql_setting
from mysql_client import MySQLClient

import secrets
import string

from logging_config import setup_module_logging

logger = setup_module_logging(__name__)

class RaceDataRunner:
    def __init__(self):
        pid = self.generate_nano_id()
        file_name = pid + ".json"
        self.base_path = Path(BASE_DIR / "data")
        self.data_file = self.base_path / file_name
        self.mysql_client = MySQLClient(mysql_setting)
        self.current_date = None

    async def start(self):
        # 连接数据库
        await self.mysql_client.connect()
        browser_runner = BrowserRunner(self.process)
        await browser_runner.start()

    async def process(self, page):
        await page.goto("https://data.7m.com.cn/fixture_data/default_big.shtml?date=1")
        table = page.locator("#fixture_tb")
        trs = table.locator("tr")
        tr_all = await trs.all()

        matches_to_insert = []

        for tr in tr_all:
            try:
                # 检查是否是日期行
                date_cell = tr.locator("td[colspan]")
                if await date_cell.count() > 0:
                    date_text = await date_cell.inner_text()
                    if date_text and date_text.strip().startswith('202'):
                        self.current_date = date_text.strip()[:10]  # 取前10位作为日期
                        logger.info(f'当前处理日期: {self.current_date}')
                        continue

                # 解析比赛数据行
                tds = tr.locator("td:not(:empty)")
                td_count = await tds.count()

                if td_count >= 4:  # 至少需要4列数据
                    td_texts = []
                    for i in range(td_count):
                        td = tds.nth(i)
                        text = await td.inner_text()
                        td_texts.append(text.strip())

                    # 解析比赛数据
                    match_data = self.parse_match_data(td_texts)
                    if match_data:
                        matches_to_insert.append(match_data)

                        # 同时发送到RabbitMQ（保持原有功能）
                        row_str = ','.join(td_texts)
                        if row_str.strip():
                            await BrowserRunnerConstants.PIKA_CLIENT.publish(
                                BrowserRunnerConstants.PIKA_CLIENT.queue, row_str
                            )

            except Exception as e:
                logger.error(f"处理行数据时出错: {e}")
                continue

        # 批量插入数据库
        if matches_to_insert:
            inserted_count = await self.mysql_client.insert_matches_batch(matches_to_insert)
            logger.info(f"成功插入 {inserted_count} 条比赛数据到数据库")

    def parse_match_data(self, td_texts: list) -> dict:
        """解析比赛数据"""
        if len(td_texts) < 4:
            return None

        try:
            # 根据数据样例: ['澳北女超', '08月19日 16:00', '紐卡素奧林匹克女足', '', '紐拉姆桐女足']
            # 格式为: [联赛, 时间, 主队, 分隔符/空, 客队]

            # 固定格式解析: [联赛, 时间, 主队, 分隔符, 客队]
            # 位置0: 联赛名称
            # 位置1: 比赛时间
            # 位置2: 主队
            # 位置3: 分隔符(忽略)
            # 位置4: 客队
            league_name = td_texts[0].strip()
            match_time_str = td_texts[1].strip()
            home_team = td_texts[2].strip()
            away_team = td_texts[4].strip()

            # 验证必要字段
            if not league_name or not home_team or not away_team:
                logger.debug(f"字段不完整，跳过: 联赛={league_name}, 主队={home_team}, 客队={away_team}, 原始数据={td_texts}")
                return None

            # 解析时间
            match_datetime = self.parse_match_time(match_time_str)
            if not match_datetime:
                logger.debug(f"时间解析失败: {match_time_str}")
                return None

            logger.info(f"解析成功: {league_name} - {home_team} vs {away_team} at {match_datetime}")

            # 比分字段保持为空（预留字段）
            return {
                'league_name': league_name,
                'home_team': home_team,
                'away_team': away_team,
                'match_time': match_datetime,
                'score': None,           # 预留字段，保持为空
                'home_score': None,      # 预留字段，保持为空
                'away_score': None,      # 预留字段，保持为空
                'match_status': '未开始'  # 默认状态
            }

        except Exception as e:
            logger.error(f"解析比赛数据时出错: {e}, 数据: {td_texts}")
            return None

    def parse_match_time(self, time_str: str) -> datetime:
        """解析比赛时间"""
        try:
            time_str = time_str.strip()
            current_year = datetime.now().year

            # 处理 "08月19日 16:00" 格式
            date_time_pattern = r'(\d{1,2})月(\d{1,2})日\s+(\d{1,2}):(\d{2})'
            match = re.match(date_time_pattern, time_str)
            if match:
                month = int(match.group(1))
                day = int(match.group(2))
                hour = int(match.group(3))
                minute = int(match.group(4))

                # 构造完整的日期时间
                try:
                    match_datetime = datetime(current_year, month, day, hour, minute, 0)
                    logger.debug(f"解析时间成功: {time_str} -> {match_datetime}")
                    return match_datetime
                except ValueError as ve:
                    logger.error(f"日期构造失败: {ve}, 年={current_year}, 月={month}, 日={day}, 时={hour}, 分={minute}")
                    return None

            # 处理简单的时间格式 "20:00"
            elif re.match(r'\d{1,2}:\d{2}', time_str):
                if not self.current_date:
                    self.current_date = datetime.now().strftime('%Y-%m-%d')

                time_part = time_str.strip()
                datetime_str = f"{self.current_date} {time_part}:00"
                return datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')

            # 处理特殊状态
            elif time_str in ['完', '推迟', '取消']:
                if not self.current_date:
                    self.current_date = datetime.now().strftime('%Y-%m-%d')

                datetime_str = f"{self.current_date} 00:00:00"
                return datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')

            # 处理纯日期格式 "08月19日"
            elif re.match(r'(\d{1,2})月(\d{1,2})日', time_str):
                date_pattern = r'(\d{1,2})月(\d{1,2})日'
                match = re.match(date_pattern, time_str)
                if match:
                    month = int(match.group(1))
                    day = int(match.group(2))

                    try:
                        match_datetime = datetime(current_year, month, day, 0, 0, 0)
                        return match_datetime
                    except ValueError as ve:
                        logger.error(f"日期构造失败: {ve}")
                        return None

            else:
                logger.debug(f"无法识别的时间格式: {time_str}")
                return None

        except Exception as e:
            logger.error(f"解析时间失败: {e}, 时间字符串: {time_str}")
            return None

    def generate_nano_id(self, length=10):
        # 定义可用字符集
        alphabet = string.ascii_letters + string.digits
        # 生成指定长度的随机字符串
        nano_id = ''.join(secrets.choice(alphabet) for _ in range(length))
        return nano_id
