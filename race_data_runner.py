import asyncio
from pathlib import Path
from datetime import datetime, timedelta
import re

from playwright.async_api import expect

from browser_runner import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from browser_runner_constants import BrowserRunnerConstants
from conf import BASE_DIR, mysql_setting
from mysql_client import MySQLClient

import secrets
import string

import logging

logger = logging.getLogger(__name__)

class RaceDataRunner:
    def __init__(self):
        pid = self.generate_nano_id()
        file_name = pid + ".json"
        self.base_path = Path(BASE_DIR / "data")
        self.data_file = self.base_path / file_name
        self.mysql_client = MySQLClient(mysql_setting)
        self.current_date = None

    async def start(self):
        # 连接数据库
        await self.mysql_client.connect()
        browser_runner = BrowserRunner(self.process)
        await browser_runner.start()

    async def process(self, page):
        await page.goto("https://data.7m.com.cn/fixture_data/default_big.shtml?date=1")
        table = page.locator("#fixture_tb")
        trs = table.locator("tr")
        tr_all = await trs.all()

        matches_to_insert = []

        for tr in tr_all:
            try:
                # 检查是否是日期行
                date_cell = tr.locator("td[colspan]")
                if await date_cell.count() > 0:
                    date_text = await date_cell.inner_text()
                    if date_text and date_text.strip().startswith('202'):
                        self.current_date = date_text.strip()[:10]  # 取前10位作为日期
                        logger.info(f'当前处理日期: {self.current_date}')
                        continue

                # 解析比赛数据行
                tds = tr.locator("td:not(:empty)")
                td_count = await tds.count()

                if td_count >= 4:  # 至少需要4列数据
                    td_texts = []
                    for i in range(td_count):
                        td = tds.nth(i)
                        text = await td.inner_text()
                        td_texts.append(text.strip())

                    # 解析比赛数据
                    match_data = self.parse_match_data(td_texts)
                    if match_data:
                        matches_to_insert.append(match_data)

                        # 同时发送到RabbitMQ（保持原有功能）
                        row_str = ','.join(td_texts)
                        if row_str.strip():
                            await BrowserRunnerConstants.PIKA_CLIENT.publish(
                                BrowserRunnerConstants.PIKA_CLIENT.queue, row_str
                            )

            except Exception as e:
                logger.error(f"处理行数据时出错: {e}")
                continue

        # 批量插入数据库
        if matches_to_insert:
            inserted_count = await self.mysql_client.insert_matches_batch(matches_to_insert)
            logger.info(f"成功插入 {inserted_count} 条比赛数据到数据库")

    def parse_match_data(self, td_texts: list) -> dict:
        """解析比赛数据"""
        if len(td_texts) < 4:
            return None

        try:
            # 根据7m.com.cn的表格结构解析数据
            # 通常格式为: [时间, 联赛, 主队, 比分, 客队, ...]

            # 查找时间列（格式如 "20:00" 或 "完"）
            time_idx = -1
            for i, text in enumerate(td_texts):
                if re.match(r'\d{2}:\d{2}', text) or text in ['完', '推迟', '取消']:
                    time_idx = i
                    break

            if time_idx == -1:
                return None

            # 基于时间列的位置来解析其他字段
            if len(td_texts) >= time_idx + 4:
                match_time_str = td_texts[time_idx]
                league_name = td_texts[time_idx + 1] if time_idx + 1 < len(td_texts) else ""
                home_team = td_texts[time_idx + 2] if time_idx + 2 < len(td_texts) else ""
                score_or_vs = td_texts[time_idx + 3] if time_idx + 3 < len(td_texts) else ""
                away_team = td_texts[time_idx + 4] if time_idx + 4 < len(td_texts) else ""

                # 如果没有明确的vs分隔符，可能比分和客队在一起
                if 'vs' not in score_or_vs.lower() and time_idx + 5 < len(td_texts):
                    away_team = td_texts[time_idx + 4]
                    score_or_vs = td_texts[time_idx + 3]

                # 清理数据
                league_name = league_name.strip()
                home_team = home_team.strip()
                away_team = away_team.strip()

                if not all([league_name, home_team, away_team]):
                    return None

                # 解析时间
                match_datetime = self.parse_match_time(match_time_str)
                if not match_datetime:
                    return None

                # 解析比分
                score, home_score, away_score, match_status = self.mysql_client.parse_score(score_or_vs)

                return {
                    'league_name': league_name,
                    'home_team': home_team,
                    'away_team': away_team,
                    'match_time': match_datetime,
                    'score': score,
                    'home_score': home_score,
                    'away_score': away_score,
                    'match_status': match_status
                }

        except Exception as e:
            logger.error(f"解析比赛数据时出错: {e}, 数据: {td_texts}")
            return None

        return None

    def parse_match_time(self, time_str: str) -> datetime:
        """解析比赛时间"""
        try:
            if not self.current_date:
                # 如果没有当前日期，使用今天
                self.current_date = datetime.now().strftime('%Y-%m-%d')

            if re.match(r'\d{2}:\d{2}', time_str):
                # 格式: "20:00"
                time_part = time_str.strip()
                datetime_str = f"{self.current_date} {time_part}:00"
                return datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
            elif time_str in ['完', '推迟', '取消']:
                # 已完成的比赛，使用当天的默认时间
                datetime_str = f"{self.current_date} 00:00:00"
                return datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
            else:
                return None

        except Exception as e:
            logger.error(f"解析时间失败: {e}, 时间字符串: {time_str}")
            return None

    def generate_nano_id(self, length=10):
        # 定义可用字符集
        alphabet = string.ascii_letters + string.digits
        # 生成指定长度的随机字符串
        nano_id = ''.join(secrets.choice(alphabet) for _ in range(length))
        return nano_id
