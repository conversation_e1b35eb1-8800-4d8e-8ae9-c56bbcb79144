#!/usr/bin/env python3
"""
测试重复数据防护功能
"""
import asyncio
from datetime import datetime
from mysql_client import MySQLClient
from conf import mysql_setting
from logging_config import setup_module_logging

logger = setup_module_logging(__name__)


async def test_duplicate_prevention():
    """测试重复数据防护"""
    logger.info("🧪 开始测试重复数据防护功能")
    
    mysql_client = MySQLClient(mysql_setting)
    
    try:
        await mysql_client.connect()
        logger.info("✅ 数据库连接成功")
        
        # 测试数据
        test_match = {
            'league_name': '测试联赛',
            'home_team': '测试主队',
            'away_team': '测试客队',
            'match_time': datetime(2025, 8, 20, 15, 0, 0),
            'home_score': None,
            'away_score': None,
            'match_status': '未开始'
        }
        
        logger.info("=" * 60)
        logger.info("📝 第一次插入测试数据")
        logger.info("=" * 60)
        
        # 第一次插入
        success1 = await mysql_client.insert_match(**test_match)
        logger.info(f"第一次插入结果: {success1}")
        
        logger.info("=" * 60)
        logger.info("📝 第二次插入相同数据（应该被跳过）")
        logger.info("=" * 60)
        
        # 第二次插入相同数据
        success2 = await mysql_client.insert_match(**test_match)
        logger.info(f"第二次插入结果: {success2}")
        
        logger.info("=" * 60)
        logger.info("📝 测试批量插入重复数据")
        logger.info("=" * 60)
        
        # 批量插入测试（包含重复和新数据）
        batch_matches = [
            # 重复数据
            test_match,
            # 新数据1
            {
                'league_name': '测试联赛',
                'home_team': '新主队1',
                'away_team': '新客队1',
                'match_time': datetime(2025, 8, 20, 16, 0, 0),
                'home_score': None,
                'away_score': None,
                'match_status': '未开始'
            },
            # 新数据2
            {
                'league_name': '测试联赛',
                'home_team': '新主队2',
                'away_team': '新客队2',
                'match_time': datetime(2025, 8, 20, 17, 0, 0),
                'home_score': None,
                'away_score': None,
                'match_status': '未开始'
            },
            # 再次重复第一条数据
            test_match
        ]
        
        inserted_count = await mysql_client.insert_matches_batch(batch_matches)
        logger.info(f"批量插入返回的新增数量: {inserted_count}")
        
        logger.info("=" * 60)
        logger.info("📊 查询测试数据验证结果")
        logger.info("=" * 60)
        
        # 查询验证
        matches = await mysql_client.get_matches_by_date('2025-08-20')
        logger.info(f"查询到的比赛数量: {len(matches)}")
        
        for match in matches:
            if '测试' in match['league_name']:
                logger.info(f"  - {match['league_name']}: {match['home_team']} vs {match['away_team']} "
                           f"at {match['match_time']}")
        
        logger.info("=" * 60)
        logger.info("🧹 清理测试数据")
        logger.info("=" * 60)
        
        # 清理测试数据
        async with mysql_client.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute("""
                    DELETE FROM race_matches 
                    WHERE league_name = '测试联赛'
                """)
                deleted_count = cursor.rowcount
                logger.info(f"清理了 {deleted_count} 条测试数据")
        
        logger.info("✅ 重复数据防护测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
    finally:
        await mysql_client.close()
        logger.info("🔌 数据库连接已关闭")


async def test_unique_constraint_details():
    """测试唯一约束的具体规则"""
    logger.info("🔍 测试唯一约束的具体规则")
    
    mysql_client = MySQLClient(mysql_setting)
    
    try:
        await mysql_client.connect()
        
        base_match = {
            'league_name': '约束测试联赛',
            'home_team': '约束主队',
            'away_team': '约束客队',
            'match_time': datetime(2025, 8, 21, 15, 0, 0),
            'score': None,
            'home_score': None,
            'away_score': None,
            'match_status': '未开始'
        }
        
        # 测试1: 相同的所有字段（应该被拒绝）
        logger.info("测试1: 完全相同的比赛")
        await mysql_client.insert_match(**base_match)
        await mysql_client.insert_match(**base_match)  # 应该被跳过
        
        # 测试2: 不同的时间（应该被接受）
        logger.info("测试2: 相同队伍，不同时间")
        different_time_match = base_match.copy()
        different_time_match['match_time'] = datetime(2025, 8, 21, 16, 0, 0)
        await mysql_client.insert_match(**different_time_match)
        
        # 测试3: 主客队交换（应该被接受）
        logger.info("测试3: 主客队交换")
        swapped_match = base_match.copy()
        swapped_match['home_team'] = '约束客队'
        swapped_match['away_team'] = '约束主队'
        await mysql_client.insert_match(**swapped_match)
        
        # 测试4: 不同联赛（应该被接受）
        logger.info("测试4: 不同联赛")
        different_league_match = base_match.copy()
        different_league_match['league_name'] = '另一个测试联赛'
        await mysql_client.insert_match(**different_league_match)
        
        # 清理测试数据
        async with mysql_client.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute("""
                    DELETE FROM race_matches 
                    WHERE league_name LIKE '%约束测试%' OR league_name LIKE '%另一个测试%'
                """)
                deleted_count = cursor.rowcount
                logger.info(f"清理了 {deleted_count} 条测试数据")
        
        logger.info("✅ 唯一约束规则测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
    finally:
        await mysql_client.close()


async def main():
    """主测试函数"""
    await test_duplicate_prevention()
    print("\n" + "="*60 + "\n")
    await test_unique_constraint_details()


if __name__ == "__main__":
    asyncio.run(main())
