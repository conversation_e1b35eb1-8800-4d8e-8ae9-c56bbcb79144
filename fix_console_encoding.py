#!/usr/bin/env python3
"""
修复Windows控制台中文编码问题
"""
import os
import sys
import subprocess
import locale


def fix_windows_console_encoding():
    """修复Windows控制台编码"""
    if os.name != 'nt':
        print("此脚本仅适用于Windows系统")
        return
    
    print("🔧 正在修复Windows控制台编码...")
    
    # 1. 设置环境变量
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    print("✅ 设置 PYTHONIOENCODING=utf-8")
    
    # 2. 设置控制台代码页为UTF-8
    try:
        result = subprocess.run(['chcp', '65001'], shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 设置控制台代码页为 UTF-8 (65001)")
        else:
            print("⚠️ 无法设置控制台代码页")
    except Exception as e:
        print(f"⚠️ 设置代码页失败: {e}")
    
    # 3. 重新配置标准输出
    try:
        if hasattr(sys.stdout, 'reconfigure'):
            sys.stdout.reconfigure(encoding='utf-8')
            print("✅ 重新配置 stdout 为 UTF-8")
        
        if hasattr(sys.stderr, 'reconfigure'):
            sys.stderr.reconfigure(encoding='utf-8')
            print("✅ 重新配置 stderr 为 UTF-8")
    except Exception as e:
        print(f"⚠️ 重新配置失败: {e}")
    
    # 4. 显示当前编码信息
    print("\n📊 当前编码信息:")
    print(f"  系统默认编码: {locale.getpreferredencoding()}")
    print(f"  stdout 编码: {sys.stdout.encoding}")
    print(f"  stderr 编码: {sys.stderr.encoding}")
    print(f"  文件系统编码: {sys.getfilesystemencoding()}")
    
    # 5. 测试中文输出
    print("\n🧪 测试中文输出:")
    test_strings = [
        "简体中文: 你好世界",
        "繁体中文: 紐卡素奧林匹克女足",
        "日文: サッカー",
        "韩文: 축구",
        "特殊符号: ⚽🏆🎯"
    ]
    
    for test_str in test_strings:
        try:
            print(f"  {test_str}")
        except UnicodeEncodeError as e:
            print(f"  ❌ 编码错误: {e}")
    
    print("\n✅ 编码修复完成!")
    print("💡 如果仍有问题，请尝试:")
    print("   1. 重启命令行窗口")
    print("   2. 使用 Windows Terminal 而不是 cmd")
    print("   3. 在 PowerShell 中运行: $OutputEncoding = [System.Text.Encoding]::UTF8")


def create_utf8_logger():
    """创建支持UTF-8的logger"""
    import logging
    import io
    
    # 创建logger
    logger = logging.getLogger('utf8_test')
    logger.setLevel(logging.INFO)
    
    # 创建格式化器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # 创建UTF-8控制台处理器
    try:
        # 方法1: 直接使用UTF-8包装器
        utf8_stdout = io.TextIOWrapper(
            sys.stdout.buffer,
            encoding='utf-8',
            errors='replace',
            line_buffering=True
        )
        console_handler = logging.StreamHandler(utf8_stdout)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        print("✅ 创建UTF-8 logger成功")
        return logger
        
    except Exception as e:
        print(f"❌ 创建UTF-8 logger失败: {e}")
        
        # 方法2: 使用标准处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        return logger


def test_utf8_logging():
    """测试UTF-8日志输出"""
    print("\n🧪 测试UTF-8日志输出:")
    
    logger = create_utf8_logger()
    
    test_messages = [
        "这是一条中文信息日志",
        "联赛名称: 澳北女超",
        "主队: 紐卡素奧林匹克女足",
        "客队: 紐拉姆桐女足",
        "解析成功: 英超 - 曼联 vs 利物浦 at 2025-08-20 15:00:00"
    ]
    
    for msg in test_messages:
        try:
            logger.info(msg)
        except Exception as e:
            print(f"❌ 日志输出失败: {e}")


if __name__ == "__main__":
    fix_windows_console_encoding()
    test_utf8_logging()
    
    input("\n按回车键退出...")
