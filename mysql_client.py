import asyncio
import logging
from datetime import datetime
from typing import Optional, Dict, Any
import aiomysql
import pymysql
from conf import MySQLConfig

logger = logging.getLogger(__name__)


class MySQLClient:
    def __init__(self, config: MySQLConfig):
        self.config = config
        self.pool = None
        self.connected = False

    async def connect(self) -> None:
        """建立数据库连接池"""
        try:
            self.pool = await aiomysql.create_pool(
                host=self.config.host,
                port=self.config.port,
                user=self.config.user,
                password=self.config.password,
                db=self.config.database,
                charset=self.config.charset,
                autocommit=True,
                minsize=1,
                maxsize=10
            )
            self.connected = True
            logger.info(f"Connected to MySQL database: {self.config.database}")
            
            # 创建表（如果不存在）
            await self.create_tables()
            
        except Exception as e:
            logger.error(f"Failed to connect to MySQL: {e}")
            raise

    async def create_tables(self) -> None:
        """创建数据库表"""
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS race_matches (
            id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
            league_name VARCHAR(255) NOT NULL COMMENT '联赛名称',
            home_team VARCHAR(255) NOT NULL COMMENT '主队名称',
            away_team VARCHAR(255) NOT NULL COMMENT '客队名称',
            match_time DATETIME NOT NULL COMMENT '比赛时间',
            match_date DATE NOT NULL COMMENT '比赛日期',
            score VARCHAR(20) DEFAULT NULL COMMENT '比分 (格式: 主队得分-客队得分)',
            home_score INT DEFAULT NULL COMMENT '主队得分',
            away_score INT DEFAULT NULL COMMENT '客队得分',
            match_status ENUM('未开始', '进行中', '已结束', '延期', '取消') DEFAULT '未开始' COMMENT '比赛状态',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            INDEX idx_league_name (league_name),
            INDEX idx_match_date (match_date),
            INDEX idx_match_time (match_time),
            INDEX idx_match_status (match_status),
            UNIQUE KEY unique_match (league_name, home_team, away_team, match_time)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='比赛数据表'
        """
        
        try:
            async with self.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(create_table_sql)
                    logger.info("Database tables created successfully")
        except Exception as e:
            logger.error(f"Failed to create tables: {e}")
            raise

    async def insert_match(self, league_name: str, home_team: str, away_team: str,
                          match_time: datetime, score: str = None, home_score: int = None,
                          away_score: int = None, match_status: str = '未开始') -> bool:
        """插入比赛数据"""
        if not self.connected or not self.pool:
            await self.connect()

        insert_sql = """
        INSERT IGNORE INTO race_matches
        (league_name, home_team, away_team, match_time, match_date, score, home_score, away_score, match_status)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """

        try:
            match_date = match_time.date()
            async with self.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(insert_sql, (
                        league_name, home_team, away_team, match_time, match_date,
                        score, home_score, away_score, match_status
                    ))
                    affected_rows = cursor.rowcount

            if affected_rows > 0:
                score_info = f" ({score})" if score else ""
                logger.info(f"Inserted match: {league_name} - {home_team} vs {away_team} at {match_time}{score_info}")
                return True
            else:
                logger.debug(f"Match already exists: {league_name} - {home_team} vs {away_team} at {match_time}")
                return False

        except Exception as e:
            logger.error(f"Failed to insert match data: {e}")
            return False

    async def insert_matches_batch(self, matches: list) -> int:
        """批量插入比赛数据"""
        if not self.connected or not self.pool:
            await self.connect()

        if not matches:
            return 0

        insert_sql = """
        INSERT IGNORE INTO race_matches
        (league_name, home_team, away_team, match_time, match_date, score, home_score, away_score, match_status)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """

        try:
            values = []
            for match in matches:
                match_date = match['match_time'].date()
                values.append((
                    match['league_name'],
                    match['home_team'],
                    match['away_team'],
                    match['match_time'],
                    match_date,
                    match.get('score'),
                    match.get('home_score'),
                    match.get('away_score'),
                    match.get('match_status', '未开始')
                ))

            async with self.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.executemany(insert_sql, values)
                    affected_rows = cursor.rowcount

            logger.info(f"Batch inserted {affected_rows} matches")
            return affected_rows

        except Exception as e:
            logger.error(f"Failed to batch insert matches: {e}")
            return 0

    def parse_score(self, score_str: str) -> tuple:
        """解析比分字符串，返回(score, home_score, away_score, match_status)"""
        if not score_str or score_str.strip() == '':
            return None, None, None, '未开始'

        score_str = score_str.strip()

        # 处理特殊状态
        if score_str in ['延期', '取消', '推迟']:
            return score_str, None, None, score_str
        if score_str in ['vs', 'VS', '-']:
            return None, None, None, '未开始'

        # 解析比分 (格式: 1-2, 0:1, 3 - 1 等)
        import re
        score_pattern = r'(\d+)[\s\-:：]+(\d+)'
        match = re.search(score_pattern, score_str)

        if match:
            home_score = int(match.group(1))
            away_score = int(match.group(2))
            formatted_score = f"{home_score}-{away_score}"
            return formatted_score, home_score, away_score, '已结束'

        return score_str, None, None, '未开始'

    async def get_matches_by_date(self, match_date: str) -> list:
        """根据日期查询比赛数据"""
        if not self.connected or not self.pool:
            await self.connect()

        select_sql = """
        SELECT id, league_name, home_team, away_team, match_time, score,
               home_score, away_score, match_status, created_at
        FROM race_matches
        WHERE match_date = %s
        ORDER BY match_time
        """

        try:
            async with self.pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    await cursor.execute(select_sql, (match_date,))
                    results = await cursor.fetchall()
                    return results
        except Exception as e:
            logger.error(f"Failed to query matches: {e}")
            return []

    async def close(self) -> None:
        """关闭数据库连接池"""
        if self.pool:
            self.pool.close()
            await self.pool.wait_closed()
            self.connected = False
            logger.info("MySQL connection pool closed")

    def __del__(self):
        """析构函数"""
        if self.pool and not self.pool._closed:
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.create_task(self.close())
            except:
                pass
