#!/usr/bin/env python3
"""
数据库管理工具
提供查看、统计和管理比赛数据的功能
"""
import asyncio
import logging
from datetime import datetime, date, timedelta
from mysql_client import MySQLClient
from conf import mysql_setting

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('database_manager.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)


class DatabaseManager:
    def __init__(self):
        self.mysql_client = MySQLClient(mysql_setting)

    async def connect(self):
        """连接数据库"""
        await self.mysql_client.connect()

    async def close(self):
        """关闭数据库连接"""
        await self.mysql_client.close()

    async def get_statistics(self):
        """获取数据库统计信息"""
        try:
            async with self.mysql_client.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    # 总比赛数
                    await cursor.execute("SELECT COUNT(*) FROM race_matches")
                    total_matches = (await cursor.fetchone())[0]
                    
                    # 按状态统计
                    await cursor.execute("""
                        SELECT match_status, COUNT(*) 
                        FROM race_matches 
                        GROUP BY match_status
                    """)
                    status_stats = await cursor.fetchall()
                    
                    # 按联赛统计
                    await cursor.execute("""
                        SELECT league_name, COUNT(*) 
                        FROM race_matches 
                        GROUP BY league_name 
                        ORDER BY COUNT(*) DESC 
                        LIMIT 10
                    """)
                    league_stats = await cursor.fetchall()
                    
                    # 最近的比赛
                    await cursor.execute("""
                        SELECT league_name, home_team, away_team, match_time, score, match_status
                        FROM race_matches 
                        ORDER BY created_at DESC 
                        LIMIT 5
                    """)
                    recent_matches = await cursor.fetchall()
                    
                    return {
                        'total_matches': total_matches,
                        'status_stats': status_stats,
                        'league_stats': league_stats,
                        'recent_matches': recent_matches
                    }
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return None

    async def get_matches_by_date_range(self, start_date: str, end_date: str):
        """根据日期范围查询比赛"""
        try:
            async with self.mysql_client.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("""
                        SELECT league_name, home_team, away_team, match_time, 
                               score, match_status, created_at
                        FROM race_matches 
                        WHERE match_date BETWEEN %s AND %s
                        ORDER BY match_time
                    """, (start_date, end_date))
                    
                    results = await cursor.fetchall()
                    return results
        except Exception as e:
            logger.error(f"查询日期范围比赛失败: {e}")
            return []

    async def get_matches_by_league(self, league_name: str, limit: int = 50):
        """根据联赛查询比赛"""
        try:
            async with self.mysql_client.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("""
                        SELECT home_team, away_team, match_time, score, match_status
                        FROM race_matches 
                        WHERE league_name = %s
                        ORDER BY match_time DESC
                        LIMIT %s
                    """, (league_name, limit))
                    
                    results = await cursor.fetchall()
                    return results
        except Exception as e:
            logger.error(f"查询联赛比赛失败: {e}")
            return []

    async def search_team_matches(self, team_name: str, limit: int = 20):
        """搜索某个队伍的比赛"""
        try:
            async with self.mysql_client.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("""
                        SELECT league_name, home_team, away_team, match_time, 
                               score, match_status
                        FROM race_matches 
                        WHERE home_team LIKE %s OR away_team LIKE %s
                        ORDER BY match_time DESC
                        LIMIT %s
                    """, (f"%{team_name}%", f"%{team_name}%", limit))
                    
                    results = await cursor.fetchall()
                    return results
        except Exception as e:
            logger.error(f"搜索队伍比赛失败: {e}")
            return []

    async def cleanup_old_data(self, days_old: int = 30):
        """清理旧数据"""
        try:
            cutoff_date = date.today() - timedelta(days=days_old)
            
            async with self.mysql_client.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("""
                        DELETE FROM race_matches 
                        WHERE match_date < %s
                    """, (cutoff_date,))
                    
                    deleted_count = cursor.rowcount
                    logger.info(f"清理了 {deleted_count} 条 {days_old} 天前的数据")
                    return deleted_count
        except Exception as e:
            logger.error(f"清理旧数据失败: {e}")
            return 0


async def print_statistics(manager):
    """打印统计信息"""
    print("\n" + "="*60)
    print("📊 数据库统计信息")
    print("="*60)
    
    stats = await manager.get_statistics()
    if not stats:
        print("❌ 无法获取统计信息")
        return
    
    print(f"📈 总比赛数: {stats['total_matches']}")
    
    print("\n🏆 按状态统计:")
    for status, count in stats['status_stats']:
        print(f"  {status}: {count}")
    
    print("\n🏟️ 热门联赛 (前10):")
    for league, count in stats['league_stats']:
        print(f"  {league}: {count}")
    
    print("\n⏰ 最近添加的比赛:")
    for match in stats['recent_matches']:
        league, home, away, match_time, score, status = match
        score_str = score if score else "vs"
        print(f"  {league}: {home} {score_str} {away} ({status}) - {match_time}")


async def interactive_menu(manager):
    """交互式菜单"""
    while True:
        print("\n" + "="*50)
        print("🗃️  数据库管理工具")
        print("="*50)
        print("1. 查看统计信息")
        print("2. 按日期查询比赛")
        print("3. 按联赛查询比赛")
        print("4. 搜索队伍比赛")
        print("5. 清理旧数据")
        print("0. 退出")
        print("-"*50)
        
        try:
            choice = input("请选择操作 (0-5): ").strip()
            
            if choice == "0":
                print("👋 再见!")
                break
            elif choice == "1":
                await print_statistics(manager)
            elif choice == "2":
                start_date = input("请输入开始日期 (YYYY-MM-DD): ").strip()
                end_date = input("请输入结束日期 (YYYY-MM-DD): ").strip()
                matches = await manager.get_matches_by_date_range(start_date, end_date)
                print(f"\n找到 {len(matches)} 场比赛:")
                for match in matches[:20]:  # 只显示前20场
                    league, home, away, match_time, score, status, created = match
                    score_str = score if score else "vs"
                    print(f"  {league}: {home} {score_str} {away} ({status}) - {match_time}")
            elif choice == "3":
                league = input("请输入联赛名称: ").strip()
                matches = await manager.get_matches_by_league(league)
                print(f"\n{league} 的比赛 (最近50场):")
                for match in matches:
                    home, away, match_time, score, status = match
                    score_str = score if score else "vs"
                    print(f"  {home} {score_str} {away} ({status}) - {match_time}")
            elif choice == "4":
                team = input("请输入队伍名称: ").strip()
                matches = await manager.search_team_matches(team)
                print(f"\n包含 '{team}' 的比赛:")
                for match in matches:
                    league, home, away, match_time, score, status = match
                    score_str = score if score else "vs"
                    print(f"  {league}: {home} {score_str} {away} ({status}) - {match_time}")
            elif choice == "5":
                days = input("请输入要清理多少天前的数据 (默认30天): ").strip()
                days = int(days) if days.isdigit() else 30
                confirm = input(f"确认要删除 {days} 天前的数据吗? (y/N): ").strip().lower()
                if confirm == 'y':
                    deleted = await manager.cleanup_old_data(days)
                    print(f"✅ 已删除 {deleted} 条记录")
                else:
                    print("❌ 操作已取消")
            else:
                print("❌ 无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n👋 再见!")
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")


async def main():
    """主函数"""
    manager = DatabaseManager()
    
    try:
        print("🔌 连接数据库...")
        await manager.connect()
        print("✅ 数据库连接成功")
        
        await interactive_menu(manager)
        
    except Exception as e:
        print(f"❌ 程序运行失败: {e}")
    finally:
        await manager.close()
        print("🔌 数据库连接已关闭")


if __name__ == "__main__":
    asyncio.run(main())
