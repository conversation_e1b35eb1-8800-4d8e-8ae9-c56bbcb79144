import asyncio
from typing import Callable, Optional

import aio_pika
import aiormq
from aio_pika import Channel, Connection


class PikaClient:
    def __init__(self, cfg):
        self.host = cfg.host
        self.port = cfg.port
        self.user = cfg.user
        self.password = cfg.password
        self.exchange_name = cfg.exchange
        self.queue = cfg.queue
        self.received_message_counter = 0
        self.sent_message_counter = 0
        self.start_time = -1
        self.connected = False
        self.connecting = False
        self.connection = None
        self.channel = None
        self.exchange = None
        self.websockets = {}
        self._consumer_tag = "id1"
        self.url = f"amqp://{self.user}:{self.password}@{self.host}:{self.port}/"
        # self.loop = loop


    async def connect(self, loop) -> None:
        try:
            self.connection: Connection = await aio_pika.connect(url=self.url, loop=loop)  # type: ignore
            self.channel = await self.connection.channel()  # type: ignore
            await self.channel.declare_queue(self.queue, durable=True)
            # await self.channel.set_qos(1)
            print('Connected to Rabbit MQ')
        except Exception as e:
            print(f"rabbitmq connect fail {e}")

    async def live_channel(self) -> Channel:
        try:
            if self.channel and not self.channel.is_closed:
                return self.channel
            await self.connect()
            return self.channel
        except Exception as e:
            print(f"rabbitmq connect fail {e}")
            raise

    async def register_callback(self, *,
                                exchange_name: str,
                                queue_name: str,
                                callback: Callable,
                                consumer_tag: str = None) -> None:
        """
        Add new callback for queue, needs to declare queue & add it to target exchange
        """
        channel = await self.live_channel()
        exchange = await channel.get_exchange(exchange_name)
        queue = await channel.declare_queue(queue_name, durable=True)

        await queue.bind(exchange)

        try:
            await queue.consume(callback, consumer_tag=consumer_tag)
        except aio_pika.exceptions.DuplicateConsumerTag:
            await self.register_callback(exchange_name=exchange_name,
                                         queue_name=queue_name,
                                         callback=callback,
                                         consumer_tag=consumer_tag)

    async def publish(self, topic, message):
        channel = await self.live_channel()
        data = aio_pika.Message(
            body=message.encode()
        )
        await channel.default_exchange.publish(
            data,
            routing_key=topic,
        )

    async def close(self) -> None:
        """
        Close Rabbit MQ connections
        """
        await self.channel.close()
        await self.connection.close()

    async def cancel(self) -> None:
        """
        Cancel consumer receive new message
        """
        await self.channel.channel.basic_cancel(self._consumer_tag)