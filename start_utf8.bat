@echo off
chcp 65001 >nul
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1

echo 🚀 启动比赛数据采集器 (UTF-8模式)
echo.

if "%1"=="test" (
    echo 🧪 运行测试...
    python test_chinese_logging.py
    pause
) else if "%1"=="manager" (
    echo 🗃️ 启动数据库管理工具...
    python database_manager.py
    pause
) else if "%1"=="fix" (
    echo 🔧 修复控制台编码...
    python fix_console_encoding.py
    pause
) else (
    echo 🌐 启动Web应用...
    python app.py
)
