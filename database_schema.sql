-- 比赛数据表结构
CREATE DATABASE IF NOT EXISTS race_data_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE race_data_db;

-- 比赛数据表
CREATE TABLE IF NOT EXISTS race_matches (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    league_name VARCHAR(255) NOT NULL COMMENT '联赛名称',
    home_team VARCHAR(255) NOT NULL COMMENT '主队名称',
    away_team VARCHAR(255) NOT NULL COMMENT '客队名称',
    match_time DATETIME NOT NULL COMMENT '比赛时间',
    match_date DATE NOT NULL COMMENT '比赛日期',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_league_name (league_name),
    INDEX idx_match_date (match_date),
    INDEX idx_match_time (match_time),
    UNIQUE KEY unique_match (league_name, home_team, away_team, match_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='比赛数据表';
