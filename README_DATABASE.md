# 数据库配置说明

## 功能概述

本项目已集成MySQL数据库支持，可以将从7m.com.cn采集到的比赛数据直接保存到MySQL数据库中。

## 数据库表结构

数据库表 `race_matches` 包含以下字段：

- `id`: 主键ID (自增)
- `league_name`: 联赛名称 (VARCHAR(255))
- `home_team`: 主队名称 (VARCHAR(255))
- `away_team`: 客队名称 (VARCHAR(255))
- `match_time`: 比赛时间 (DATETIME)
- `match_date`: 比赛日期 (DATE)
- `score`: 比分字符串 (VARCHAR(20), 格式: "2-1")
- `home_score`: 主队得分 (INT)
- `away_score`: 客队得分 (INT)
- `match_status`: 比赛状态 (ENUM: '未开始', '进行中', '已结束', '延期', '取消')
- `created_at`: 创建时间 (TIMESTAMP)
- `updated_at`: 更新时间 (TIMESTAMP)

## 配置步骤

### 1. 安装MySQL依赖

```bash
pip install aiomysql pymysql
```

### 2. 创建数据库

使用提供的 `database_schema.sql` 文件创建数据库和表：

```bash
mysql -u root -p < database_schema.sql
```

### 3. 配置数据库连接

在 `dev.yml` 文件中配置MySQL连接信息：

```yaml
mysql_db:
  host: localhost          # 数据库主机地址
  port: 3306              # 数据库端口
  user: root              # 数据库用户名
  password: your_password # 数据库密码
  database: race_data_db  # 数据库名称
  charset: utf8mb4        # 字符集
```

### 4. 环境变量配置 (可选)

您也可以通过环境变量来配置数据库连接：

```bash
export mysql_host=localhost
export mysql_port=3306
export mysql_user=root
export mysql_password=your_password
export mysql_database=race_data_db
export mysql_charset=utf8mb4
```

## 使用方法

### 运行数据采集

```bash
python app.py
```

然后调用API接口：

```bash
curl -X POST http://localhost:8989/get_races
```

### 测试数据库功能

运行测试脚本验证数据库功能：

```bash
python test_database.py
```

## 功能特性

1. **自动表创建**: 首次连接时自动创建数据库表
2. **重复数据防护**: 使用UNIQUE约束防止重复插入相同比赛
3. **批量插入**: 支持批量插入提高性能
4. **比分解析**: 自动解析多种比分格式 (2-1, 0:3, 1 - 2等)
5. **状态管理**: 根据比分情况自动判断比赛状态
6. **连接池**: 使用连接池管理数据库连接
7. **错误处理**: 完善的错误处理和日志记录

## 数据流程

1. 爬虫从7m.com.cn采集比赛数据
2. 解析HTML表格提取比赛信息
3. 解析比分和时间信息
4. 批量保存到MySQL数据库
5. 同时发送到RabbitMQ队列 (保持原有功能)

## 日志配置

项目已优化日志配置以支持中文输出：

- **UTF-8编码**: 所有日志文件使用UTF-8编码
- **控制台输出**: 支持中文字符正常显示
- **文件轮转**: 自动轮转日志文件，避免文件过大
- **模块化日志**: 不同模块使用独立的日志文件

### 解决Windows控制台中文乱码

如果在Windows系统中遇到控制台中文乱码问题，请使用以下方法：

#### 方法1: 使用修复脚本
```bash
python fix_console_encoding.py
```

#### 方法2: 使用UTF-8启动脚本
```bash
# 启动主应用
python start_with_utf8.py app

# 运行测试
python start_with_utf8.py test

# 启动管理工具
python start_with_utf8.py manager
```

#### 方法3: 使用批处理文件 (Windows)
```cmd
# 启动应用
start_utf8.bat

# 运行测试
start_utf8.bat test

# 启动管理工具
start_utf8.bat manager

# 修复编码
start_utf8.bat fix
```

#### 方法4: 手动设置环境变量
```cmd
chcp 65001
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1
python app.py
```

### 测试中文日志

```bash
python test_chinese_logging.py
```

## 注意事项

- 确保MySQL服务正在运行
- 确保数据库用户有创建表和插入数据的权限
- 建议在生产环境中使用专门的数据库用户而不是root用户
- 定期备份数据库数据
- 如果遇到中文乱码，请确保系统环境变量 `PYTHONIOENCODING=utf-8`
