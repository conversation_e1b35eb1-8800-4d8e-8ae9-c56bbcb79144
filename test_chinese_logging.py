#!/usr/bin/env python3
"""
测试中文日志输出
"""
from logging_config import setup_module_logging

def test_chinese_logging():
    """测试中文日志功能"""
    logger = setup_module_logging("chinese_test")
    
    print("=" * 60)
    print("🧪 测试中文日志输出")
    print("=" * 60)
    
    # 测试基本中文
    logger.info("这是一条中文信息日志")
    logger.warning("这是一条中文警告日志")
    logger.error("这是一条中文错误日志")
    
    # 测试比赛数据相关的中文
    logger.info("联赛名称: 澳北女超")
    logger.info("主队: 紐卡素奧林匹克女足")
    logger.info("客队: 紐拉姆桐女足")
    logger.info("比赛时间: 08月19日 16:00")
    
    # 测试解析过程日志
    sample_data = ['澳北女超', '08月19日 16:00', '紐卡素奧林匹克女足', '', '紐拉姆桐女足']
    logger.info(f"解析数据样例: {sample_data}")
    logger.info("解析成功: 澳北女超 - 紐卡素奧林匹克女足 vs 紐拉姆桐女足 at 2025-08-19 16:00:00")
    
    # 测试各种中文字符
    logger.info("测试繁体中文: 臺灣、香港、澳門")
    logger.info("测试日文: サッカー、フットボール")
    logger.info("测试韩文: 축구、풋볼")
    logger.info("测试特殊符号: ⚽🏆🎯🥅")
    
    # 测试错误信息
    logger.error("数据库连接失败: 无法连接到MySQL服务器")
    logger.warning("字段不完整，跳过: 联赛=英超, 主队=, 客队=利物浦")
    
    print("\n" + "=" * 60)
    print("✅ 中文日志测试完成")
    print("📁 请检查 logs/chinese_test.log 文件")
    print("=" * 60)

if __name__ == "__main__":
    test_chinese_logging()
